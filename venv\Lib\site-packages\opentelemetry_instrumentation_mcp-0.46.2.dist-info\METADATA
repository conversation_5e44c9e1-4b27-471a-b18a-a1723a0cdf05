Metadata-Version: 2.3
Name: opentelemetry-instrumentation-mcp
Version: 0.46.2
Summary: OpenTelemetry mcp instrumentation
License: Apache-2.0
Author: <PERSON>
Author-email: <EMAIL>
Requires-Python: >=3.10,<4
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Provides-Extra: instruments
Requires-Dist: opentelemetry-api (>=1.28.0,<2.0.0)
Requires-Dist: opentelemetry-exporter-otlp (>=1.34.1,<2.0.0)
Requires-Dist: opentelemetry-instrumentation (>=0.50b0)
Requires-Dist: opentelemetry-semantic-conventions (>=0.50b0)
Requires-Dist: opentelemetry-semantic-conventions-ai (>=0.4.13,<0.5.0)
Project-URL: Repository, https://github.com/traceloop/openllmetry/tree/main/packages/opentelemetry-instrumentation-mcp
Description-Content-Type: text/markdown

OpenTelemetry MCP Instrumentation

<a href="https://pypi.org/project/opentelemetry-instrumentation-mcp/">
    <img src="https://badge.fury.io/py/opentelemetry-instrumentation-mcp.svg">
</a>

This library allows tracing of agentic workflows implemented with MCP framework [mcp python sdk](https://github.com/modelcontextprotocol/python-sdk).

## Installation

```bash
pip install opentelemetry-instrumentation-mcp
```

## Example usage

```python
from opentelemetry.instrumentation.mcp import McpInstrumentor

McpInstrumentor().instrument()
```

## Privacy

**By default, this instrumentation logs prompts, completions, and embeddings to span attributes**. This gives you a clear visibility into how your LLM application tool usage is working, and can make it easy to debug and evaluate the tool usage.

However, you may want to disable this logging for privacy reasons, as they may contain highly sensitive data from your users. You may also simply want to reduce the size of your traces.

To disable logging, set the `TRACELOOP_TRACE_CONTENT` environment variable to `false`.

```bash
TRACELOOP_TRACE_CONTENT=false
```

