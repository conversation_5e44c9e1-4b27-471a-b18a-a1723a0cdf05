import urllib.request
import json

print("Testing FastAPI Server...")
print("=" * 50)

try:
    # Test root endpoint
    print("Testing: GET /")
    response = urllib.request.urlopen('http://localhost:8000/')
    data = response.read().decode()
    print(f"Status: {response.getcode()}")
    print(f"Response: {data}")
    print()
    
    # Test docs endpoint
    print("Testing: GET /docs")
    response = urllib.request.urlopen('http://localhost:8000/docs')
    print(f"Status: {response.getcode()}")
    print("Docs endpoint is accessible!")
    print()
    
    # Test query endpoint
    print("Testing: POST /main-query-function")
    query_data = json.dumps({"query": "test query"}).encode()
    req = urllib.request.Request('http://localhost:8000/main-query-function', 
                                data=query_data,
                                headers={'Content-Type': 'application/json'})
    response = urllib.request.urlopen(req)
    data = response.read().decode()
    print(f"Status: {response.getcode()}")
    print(f"Response: {data}")
    
    print("\n" + "=" * 50)
    print("✅ All tests completed successfully!")
    print("Your API is working correctly!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("Make sure the server is running on http://localhost:8000")
