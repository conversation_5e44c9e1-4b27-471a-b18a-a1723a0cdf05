import os
from azure.search.documents import Search<PERSON>lient
from azure.core.credentials import AzureKeyCredential
from fastapi import HTTP<PERSON>x<PERSON>

from services.document_service import (
    data_loader,
    delete_files_and_subdirectories
)
from config import (AZURE_COGNITIVE_SEARCH_API_KEY, AZURE_COGNITIVE_SEARCH_ENDPOINT, AZURE_COGNITIVE_SEARCH_INDEX_NAME)

def is_file_name_in_index(file_name: str, user_id: str) -> bool:
    """
    Checks if a document with the given file name already exists in the Azure index.
    """
    credential = AzureKeyCredential(AZURE_COGNITIVE_SEARCH_API_KEY)
    search_client = SearchClient(AZURE_COGNITIVE_SEARCH_ENDPOINT, AZURE_COGNITIVE_SEARCH_INDEX_NAME, credential=credential)

    # Construct the filter to check for the file name
    # The file_name field must be filterable in your index schema.
    filter_string = f"user_id eq '{user_id}' and source eq '{file_name}'"

    try:
        # Perform a search with the filter
        # We only need one result, so we set top=1 for efficiency.
        results = search_client.search(search_text="*", filter=filter_string, top=1)
        
        # If the result count is greater than 0, the file already exists.
        return len(list(results)) > 0

    except Exception as e:
        print(f"An error occurred while checking for the file: {e}")
        return False

async def upload_and_process_files(files, output_directory, userid):
    try:
        # Save all uploaded files
        saved_files = []
        rejected_files = []
        print("process step")
        for file in files:
            if is_file_name_in_index(f"temp_data\\{file.filename}", userid):
                rejected_files.append(file.filename)
                continue
            file_path = os.path.join(output_directory, file.filename)
            with open(file_path, "wb") as f:
                content = await file.read()
                f.write(content)
            saved_files.append(file_path)
        
        if len(saved_files) > 0:
            # Process the files with userid using athena-index-4
            print("data loader step")
            data_loader(
                folder_path=output_directory, 
                AZURE_COGNITIVE_SEARCH_INDEX_NAME=AZURE_COGNITIVE_SEARCH_INDEX_NAME,
                userid=userid
            )
            print("after data loader")
            # Clean up
            delete_files_and_subdirectories(output_directory)
        return rejected_files, saved_files

    except Exception as e:
        # Clean up on error
        if os.path.exists(output_directory):
            delete_files_and_subdirectories(output_directory)
        raise HTTPException(status_code=500, detail=f"Error processing files: {str(e)}")