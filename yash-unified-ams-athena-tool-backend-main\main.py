from dotenv import load_dotenv
load_dotenv()
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from middleware.auth_middleware import KeycloakAuthMiddleware
from routers import upload_router, query_router, recommendation_router
from telemetry.opentelemetry_setup import init_otel
from telemetry.traceloop_setup import init_traceloop
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from middleware.tiktoken import TokenCounterMiddleware

# Load environment variables
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="Document Intelligence API",
    description="API for document upload, processing, and intelligent querying",
    version="1.0.0"
)

# Init OTEL & Traceloop
init_traceloop()
init_otel(app)
FastAPIInstrumentor().instrument_app(app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add Keycloak middleware
app.add_middleware(KeycloakAuthMiddleware)
app.add_middleware(TokenCounterMiddleware)

# Add a root endpoint
@app.get("/")
async def root():
    """
    Welcome endpoint for the Document Intelligence API
    """
    return {
        "message": "Welcome to Document Intelligence API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "docs": "/docs",
            "upload": "/upload",
            "query": "/query",
            "recommendations": "/recommendations"
        }
    }

# Include routers
app.include_router(upload_router.router, tags=["Upload"])
app.include_router(query_router.router, tags=["Query"])
app.include_router(recommendation_router.router, tags=["Recommendations"])
