#!/usr/bin/env python3
"""
API Testing Script for FastAPI Backend
This script tests all the available endpoints of your FastAPI application.
"""

import urllib.request
import urllib.parse
import json
import time
import sys

# Configuration
BASE_URL = "http://localhost:8000"
TIMEOUT = 10

def test_endpoint(method, endpoint, data=None, files=None, headers=None):
    """Test a single API endpoint"""
    url = f"{BASE_URL}{endpoint}"

    print(f"\n{'='*60}")
    print(f"Testing: {method} {endpoint}")
    print(f"URL: {url}")

    try:
        if method.upper() == "GET":
            req = urllib.request.Request(url)
            if headers:
                for key, value in headers.items():
                    req.add_header(key, value)

            with urllib.request.urlopen(req, timeout=TIMEOUT) as response:
                status_code = response.getcode()
                response_headers = dict(response.headers)
                response_body = response.read().decode('utf-8')

        elif method.upper() == "POST":
            if data:
                post_data = json.dumps(data).encode('utf-8')
                req = urllib.request.Request(url, data=post_data)
                req.add_header('Content-Type', 'application/json')
            else:
                req = urllib.request.Request(url, data=b'')

            if headers:
                for key, value in headers.items():
                    req.add_header(key, value)

            with urllib.request.urlopen(req, timeout=TIMEOUT) as response:
                status_code = response.getcode()
                response_headers = dict(response.headers)
                response_body = response.read().decode('utf-8')
        else:
            print(f"Unsupported method: {method}")
            return False

        print(f"Status Code: {status_code}")
        print(f"Response Headers: {response_headers}")

        try:
            response_json = json.loads(response_body)
            print(f"Response Body: {json.dumps(response_json, indent=2)}")
        except:
            print(f"Response Body (text): {response_body}")

        return status_code < 400

    except urllib.error.URLError as e:
        if "Connection refused" in str(e) or "No connection could be made" in str(e):
            print("❌ ERROR: Could not connect to server. Make sure the server is running!")
        else:
            print(f"❌ ERROR: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

def main():
    """Main testing function"""
    print("🚀 Starting API Tests...")
    print(f"Base URL: {BASE_URL}")
    
    # Test 1: Root endpoint
    print("\n" + "="*80)
    print("TEST 1: Root Endpoint")
    success1 = test_endpoint("GET", "/")
    
    # Test 2: API Documentation
    print("\n" + "="*80)
    print("TEST 2: API Documentation")
    success2 = test_endpoint("GET", "/docs")
    
    # Test 3: OpenAPI Schema
    print("\n" + "="*80)
    print("TEST 3: OpenAPI Schema")
    success3 = test_endpoint("GET", "/openapi.json")
    
    # Test 4: Query endpoint
    print("\n" + "="*80)
    print("TEST 4: Query Endpoint")
    query_data = {
        "query": "What is the main topic of the documents?"
    }
    success4 = test_endpoint("POST", "/main-query-function", data=query_data)
    
    # Test 5: Upload endpoint (without file for now)
    print("\n" + "="*80)
    print("TEST 5: Upload Endpoint (GET method)")
    success5 = test_endpoint("GET", "/upload")
    
    # Test 6: Recommendations endpoint
    print("\n" + "="*80)
    print("TEST 6: Recommendations Endpoint")
    success6 = test_endpoint("GET", "/recommendations")
    
    # Summary
    print("\n" + "="*80)
    print("📊 TEST SUMMARY")
    print("="*80)
    
    tests = [
        ("Root Endpoint", success1),
        ("API Documentation", success2),
        ("OpenAPI Schema", success3),
        ("Query Endpoint", success4),
        ("Upload Endpoint", success5),
        ("Recommendations", success6)
    ]
    
    passed = sum(1 for _, success in tests if success)
    total = len(tests)
    
    for test_name, success in tests:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<20} {status}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your API is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the server logs for more details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
