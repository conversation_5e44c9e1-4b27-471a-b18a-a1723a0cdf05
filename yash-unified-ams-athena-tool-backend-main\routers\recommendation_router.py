import json
from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Form, Security, status
from fastapi.security import <PERSON><PERSON>eyHeader

from config import API_KEYS
from schemas.api_schema import RequestBody
from services.recommendation_service import next_question_prediction

router = APIRouter()

@router.post("/next-question-recommendation")
async def next_question_recommendation(
    userRequest: RequestBody
):
    """
    Get next question recommendations based on conversation history
    
    - **query**: JSON string of conversation history
    - **userid**: Optional user identifier (not used in recommendation logic but maintained for consistency)
    
    Expected query format:
    ```json
    [
        {
            "question": "What is machine learning?",
            "answer": "Machine learning is a field of AI..."
        },
        {
            "question": "What are supervised learning algorithms?",
            "answer": "Supervised learning algorithms are..."
        }
    ]
    ```
    """
    
    try:
        # Parse conversation history JSON
        history_dict = json.loads(userRequest.query)
        
        # Validate the format
        if not isinstance(history_dict, list):
            raise HTTPException(
                status_code=400, 
                detail="Query must be a list of question-answer pairs"
            )
        
        for item in history_dict:
            if not isinstance(item, dict) or "question" not in item or "answer" not in item:
                raise HTTPException(
                    status_code=400,
                    detail="Each item in query must have 'question' and 'answer' fields"
                )
        
        # Generate recommendations
        recommended_questions = next_question_prediction(history_dict, no_of_questions=4)
        
        result = {
            "recommended_questions": recommended_questions,
            "count": len(recommended_questions)
        }
        
        return result
        
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=400, 
            detail="Invalid JSON format in query"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error generating recommendations: {str(e)}"
        )