import json
import os
import uuid
import pandas as pd
import glob
import shutil
from time import sleep
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.document_loaders import CSVLoader
from langchain_community.vectorstores import AzureSearch
from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import CharacterTextSplitter
from langchain_community.document_loaders import Docx2txtLoader
from langchain.schema import Document
from pptx import Presentation
from azure.search.documents.indexes.models import (
    SearchableField,
    SearchField,
    SearchFieldDataType,
    SimpleField
)
from deep_translator import GoogleTranslator

from config import (GOOGLE_API_KEY, MU<PERSON><PERSON>INGUAL, EMBEDDING_MODEL_NAME, AZURE_COGNITIVE_SEARCH_API_KEY,  AZURE_COGNITIVE_SEARCH_ENDPOINT)


def translated_text_english(text):
    translated_text = GoogleTranslator(source='auto', target='en').translate(text)
    return translated_text


def delete_files_and_subdirectories(directory_path):
   try:
     with os.scandir(directory_path) as entries:
       for entry in entries:
         if entry.is_file():
            os.unlink(entry.path)
         else:
            shutil.rmtree(entry.path)
     print("All files and subdirectories deleted successfully.")
   except OSError:
     print("Error occurred while deleting files and subdirectories.")


def data_loader(folder_path, AZURE_COGNITIVE_SEARCH_INDEX_NAME, userid):
    
    # Google Gemini Embedding
    embeddings = GoogleGenerativeAIEmbeddings(
        model=EMBEDDING_MODEL_NAME,
        google_api_key=GOOGLE_API_KEY
    )
    
    embedding_function = embeddings.embed_query
    
    fields = [
        SimpleField(
            name="id",
            type=SearchFieldDataType.String,
            key=True,
            filterable=True,
            retrievable=True
        ),
        SearchableField(
            name="content",
            type=SearchFieldDataType.String,
            searchable=True,
            retrievable=True
        ),
        SearchField(
            name="content_vector",
            type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
            searchable=True,
            vector_search_dimensions=len(embedding_function("Text")),
            vector_search_profile_name="myHnswProfile",
            retrievable=True
        ),
        SearchableField(
            name="metadata",
            type=SearchFieldDataType.String,
            searchable=True,
            retrievable=True
        ),
        # Additional field for filtering on document source
        SimpleField(
            name="source",
            type=SearchFieldDataType.String,
            filterable=True,
            retrievable=True
        ),
        # NEW: Add userid field for user-specific filtering
        SimpleField(
            name="user_id",
            type=SearchFieldDataType.String,
            filterable=True,
            retrievable=True
        ),
    ]

    
    vectordb = AzureSearch(azure_search_endpoint=AZURE_COGNITIVE_SEARCH_ENDPOINT,
                            azure_search_key=AZURE_COGNITIVE_SEARCH_API_KEY,
                            index_name=AZURE_COGNITIVE_SEARCH_INDEX_NAME,
                            embedding_function=embedding_function,
                            fields=fields)

    def processed_documents(documents, userid):
        # Preprocess documents
        pro_docs = []
        for doc in documents:
            doc_metadata = doc.metadata.copy()
            # Add unique ID if not present
            doc_metadata['id'] = doc_metadata.get('id', str(uuid.uuid4()))
            # Serialize metadata to JSON string
            doc_metadata['metadata'] = json.dumps(doc.metadata)

            # Ensure source is set
            doc_metadata['source'] = doc_metadata.get('source', '')
            
            # NEW: Add userid to metadata
            doc_metadata['user_id'] = userid
            
            print(doc_metadata)
            # Create new Document with updated metadata
            processed_doc = Document(
                page_content=doc.page_content.strip(),  # Remove leading/trailing whitespace
                metadata=doc_metadata
            )
            pro_docs.append(processed_doc)
            
        return pro_docs
    
    def list_files_glob(path = folder_path, pattern='/**/*', recursive=True, file_type = '.csv'):
        pattern = path + pattern + file_type
        files = glob.glob(pattern, recursive=recursive)
        return files

    
    def load_and_combine_csv(folder_path):
        all_document = []
        for filename in list_files_glob(file_type = '.csv'):
            if filename.endswith('.csv'):
                file_path = os.path.join(filename)
                # print(f'<p style="font-size:9px; color:yellow;">{file_path}</p>', unsafe_allow_html=True)
                try:
                    loader = CSVLoader(file_path, encoding="windows-1252")
                    documents = loader.load()
                    all_document = all_document+documents
                except:
                    pass
        return all_document
    
    documents = load_and_combine_csv(folder_path)

    if MULTILINGUAL:
        for i in range(len(documents)):
            documents[i].page_content = translated_text_english(documents[i].page_content)

    processed_docs = processed_documents(documents, userid)
    vectordb.add_documents(processed_docs)
    print("length of documents------>", len(documents))
    if len(processed_docs) > 0:
        sleep(3)  

    def load_and_combine_pdfs(folder_path):
        all_pages = []
        for filename in list_files_glob(file_type = '.pdf'):
            if filename.endswith('.pdf'):
                file_path = os.path.join(filename)
                try:
                    loader = PyPDFLoader(file_path)
                    pages = loader.load_and_split()
                    all_pages.extend(pages)
                except:
                    pass
        return all_pages
    
    combined_pages = load_and_combine_pdfs(folder_path)
    text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=10)
    pdf_chunks = text_splitter.split_documents(combined_pages)

    if MULTILINGUAL:
        for i in range(len(pdf_chunks)):
            pdf_chunks[i].page_content = translated_text_english(pdf_chunks[i].page_content)

    processed_docs = processed_documents(pdf_chunks, userid)
    vectordb.add_documents(processed_docs)
    print("pdf chunks---->", len(processed_docs))
    if len(processed_docs) > 0:
        sleep(3)  

    def load_and_combine_docs(folder_path):
        all_pages = []
        for filename in list_files_glob(file_type = '.docx'):
            if filename.endswith('.docx'):
                file_path = os.path.join(filename)
                try:
                    loader = Docx2txtLoader(file_path)
                    pages = loader.load()
                    all_pages.extend(pages)
                except:
                    pass
        return all_pages
    
    
    combined_pages = load_and_combine_docs(folder_path)
    text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=10)
    docs_chunks = text_splitter.split_documents(combined_pages)

    if MULTILINGUAL:
        for i in range(len(docs_chunks)):
            docs_chunks[i].page_content = translated_text_english(docs_chunks[i].page_content)
    
    processed_docs = processed_documents(docs_chunks, userid)
    vectordb.add_documents(processed_docs)
    print("docs chunks---->", len(processed_docs))
    if len(processed_docs) > 0:
        sleep(3)  
    
    class CustomPptxLoader:
        def __init__(self, file_path: str):
            self.file_path = file_path
    
        def load(self):
            prs = Presentation(self.file_path)
            documents = []
            
            for slide_number, slide in enumerate(prs.slides):
                slide_text = []
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        slide_text.append(shape.text)
                
                page_content = "\n".join(slide_text)
                documents.append(Document(page_content=page_content, metadata={"source": self.file_path}))
            
            return documents
    
    def load_and_combine_pptx(folder_path):
        all_pages = []
        for filename in list_files_glob(file_type = '.pptx'):
            if filename.endswith('.pptx'):
                file_path = os.path.join(filename)
                try:
                    loader = CustomPptxLoader(file_path)
                    pages = loader.load()
                    all_pages.extend(pages)
                except:
                    pass
        return all_pages
    
    combined_pages = load_and_combine_pptx(folder_path)
    text_splitter = CharacterTextSplitter(chunk_size=500, chunk_overlap=5)
    ppt_chunks = text_splitter.split_documents(combined_pages)

    if MULTILINGUAL:
        for i in range(len(ppt_chunks)):
            ppt_chunks[i].page_content = translated_text_english(ppt_chunks[i].page_content)
    
    processed_docs = processed_documents(ppt_chunks, userid)
    vectordb.add_documents(processed_docs)
    print("ppt chunks---->", len(processed_docs))
    if len(ppt_chunks) > 0:
        sleep(3)  

    class CustomExcelLoader:
        def __init__(self, file_path: str):
            self.file_path = file_path
    
        def load(self):
            # Read all sheets from the Excel file
            dfs = pd.read_excel(self.file_path, sheet_name=None)
            dfs = {sheet: df.dropna(axis=1, thresh=2) for sheet, df in dfs.items()}
            documents = []
    
            # Iterate over each sheet and then over each row in the DataFrame
            for sheet_name, df in dfs.items():
                df = df.dropna(how='all')
                
                for index, row in df.iterrows():
                    content = row.to_string()
                    documents.append(Document(page_content=content, metadata={"source": self.file_path}))
    
            return documents
    
    def load_and_combine_excel(folder_path):
        all_pages = []
        for filename in list_files_glob(file_type = '.xlsx'):
            if filename.endswith('.xlsx'):
                file_path = os.path.join(filename)
                loader = CustomExcelLoader(file_path)
                pages = loader.load()
                all_pages.extend(pages)
    
        return all_pages
    
    combined_pages = load_and_combine_excel(folder_path)

    if MULTILINGUAL:
        for i in range(len(combined_pages)):
            combined_pages[i].page_content = translated_text_english(combined_pages[i].page_content)

    processed_docs = processed_documents(combined_pages, userid)
    vectordb.add_documents(processed_docs)

    print("combined pages---->", len(processed_docs))
    return