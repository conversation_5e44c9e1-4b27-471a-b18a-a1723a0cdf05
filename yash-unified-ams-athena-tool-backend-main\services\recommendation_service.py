from typing import List, Dict
from fastapi import H<PERSON>PException
from config import GOO<PERSON>LE_API_KEY, GEMINI_MODEL_NAME
import google.generativeai as genai


def next_question_prediction(history_dict: List[Dict[str, str]], no_of_questions: int = 4) -> List[str]:
    """Generate next question recommendations based on conversation history"""

    try:
        model = genai.GenerativeModel(GEMINI_MODEL_NAME)

        def convert_dict_to_conversation_history(data):
            conversation_history = ""
            for item in data:
                question = item["question"]
                answer = item["answer"]
                conversation_history += f'"question": "{question}"\n'
                conversation_history += f'"answer": "{answer}"\n\n'
            return conversation_history

        conversation_history = convert_dict_to_conversation_history(history_dict)

        prompt_to_use = f"Suggest next {no_of_questions} question as user suppose to ask bot without numbers same language as the user's question and separate each question by new line (plain text only with no extra character), please don't add tag 'question:' in response"
        
        full_prompt = f"{prompt_to_use}\n\nConversation History:\n{conversation_history}"
        
        response = model.generate_content(full_prompt)
        return response.text.split('\n')
    
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Error generating recommendations: {str(e)}"
        )