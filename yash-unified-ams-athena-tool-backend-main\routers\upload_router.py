import os
from fastapi import APIRouter, File, UploadFile, HTTPException, Request
from typing import List

import jwt

from config import TEMP_DATA_DIR
from services.upload_service import upload_and_process_files

router = APIRouter()

@router.post("/upload")
async def upload_files(
    request: Request,  # NEW: Add userid as required form parameter
    files: List[UploadFile] = File(...)
):
    """
    Upload and process multiple files into Azure Cognitive Search (athena-index-4)
    
    - **userid**: User identifier for document ownership (required)
    - **files**: List of files to upload (CSV, PDF, DOCX, PPTX, XLSX)
    """
    
    # Extract user_id from JWT token if available, otherwise use default
    auth_header = request.headers.get("authorization")
    if auth_header:
        token = auth_header.partition(" ")[2]
        try:
            user_id = jwt.decode(
                token,
                options={"verify_signature": False},  # Skip signature verification
                algorithms=["HS256", "RS256", "ES256"]  # Allow common algorithms
            ).get("email")
        except Exception as e:
            print(f"JWT decode error: {e}")
            user_id = "<EMAIL>"  # Default user for testing
    else:
        user_id = "<EMAIL>"  # Default user for testing
    print("Upload file step")
    if not files:
        raise HTTPException(status_code=400, detail="At least one file is required")
    
    output_directory = TEMP_DATA_DIR
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)
    
    try:
        rejected_files, saved_files = await upload_and_process_files(files, output_directory, user_id)

        return {
            "message": f"Uploaded and processed {len(files)} files successfully for user {user_id}",
            "files_processed": saved_files,
            "rejected_files": rejected_files
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")