#!/usr/bin/env python3
"""
Server Starter Sc<PERSON>t
This script starts the FastAPI server programmatically.
"""

import uvicorn
import os
import sys

def start_server():
    """Start the FastAPI server"""
    print("🚀 Starting FastAPI Server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔄 Auto-reload is enabled")
    print("\nPress Ctrl+C to stop the server")
    print("="*60)
    
    try:
        # Change to the correct directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        main_dir = os.path.join(script_dir, "yash-unified-ams-athena-tool-backend-main")
        
        if os.path.exists(main_dir):
            os.chdir(main_dir)
            print(f"📁 Changed directory to: {main_dir}")
        else:
            print(f"📁 Using current directory: {script_dir}")
        
        # Start the server
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_server()
