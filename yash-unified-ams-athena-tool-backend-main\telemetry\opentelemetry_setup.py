import os
import logging
from opentelemetry import trace, metrics
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.exporter.otlp.proto.http.metric_exporter import OTLPMetricExporter
from opentelemetry.exporter.otlp.proto.http._log_exporter import OTLPLogExporter
from opentelemetry.sdk._logs import LoggerProvider, LoggingHandler
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
from opentelemetry._logs import set_logger_provider
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from dotenv import load_dotenv

load_dotenv()
OTLP_ENDPOINT = os.getenv("OTLP_ENDPOINT")
 
resource = Resource(attributes={
    SERVICE_NAME: os.getenv("OTEL_SERVICE_NAME"),
    "service.version": "1.0.0",
    "service.instance.id": "Athena-backend-instance-1"
})
 
# ========= Tracing =========
trace_provider = TracerProvider(resource=resource)
trace.set_tracer_provider(trace_provider)
 
trace_exporter = OTLPSpanExporter(endpoint=f"{OTLP_ENDPOINT}/v1/traces")
trace_provider.add_span_processor(BatchSpanProcessor(trace_exporter))
 
# ========= Metrics =========
metric_exporter = OTLPMetricExporter(endpoint=f"{OTLP_ENDPOINT}/v1/metrics")
metric_reader = PeriodicExportingMetricReader(metric_exporter, export_interval_millis=30000)
metrics_provider = MeterProvider(resource=resource, metric_readers=[metric_reader])
metrics.set_meter_provider(metrics_provider)
 
meter = metrics.get_meter("ai-factory-meter")
 
request_counter = meter.create_counter(
    "http_requests_total",
    description="Total number of HTTP requests",
    unit="1"
)
 
request_duration = meter.create_histogram(
    "http_request_duration_seconds",
    description="HTTP request duration in seconds",
    unit="s"
)
 
# ========= Logging =========
log_exporter = OTLPLogExporter(endpoint=f"{OTLP_ENDPOINT}/v1/logs")
logger_provider = LoggerProvider(resource=resource)
set_logger_provider(logger_provider)
logger_provider.add_log_record_processor(BatchLogRecordProcessor(log_exporter))
 
otel_handler = LoggingHandler(level=logging.NOTSET, logger_provider=logger_provider)
logging.getLogger().addHandler(otel_handler)
 
# Standard logging format
logging.basicConfig(
level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
 
# Enable OpenTelemetry Logging
LoggingInstrumentor().instrument(set_logging_format=True)
 
# ========= Public APIs =========
def init_otel(app):
    FastAPIInstrumentor.instrument_app(app)
    RequestsInstrumentor().instrument()
 
def get_metrics():
    return request_counter, request_duration