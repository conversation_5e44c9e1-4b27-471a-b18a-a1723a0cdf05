from langchain_core.prompts import PromptTemplate
from langchain.memory import Conversation<PERSON>ufferMemory
from langchain.chains import RetrievalQA
from langchain_community.vectorstores import AzureSearch
from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings
from config import (
                        GEMINI_MODEL_NAME, GOOGLE_API_KEY, EMBEDDING_MODEL_NAME, 
                        AZURE_COGNITIVE_SEARCH_API_KEY,  AZURE_COGNITIVE_SEARCH_ENDPOINT, AZURE_COGNITIVE_SEARCH_INDEX_NAME
                )
from azure.search.documents.indexes.models import (
                                                    SearchableField,
                                                    SearchField,
                                                    SearchFieldDataType,
                                                    SimpleField
                                                )
from azure.search.documents.indexes import SearchIndexClient
from azure.core.credentials import AzureKeyCredential
import google.generativeai as genai


multilingual = True


def detect_language(text):
    """Detect language using Gemini"""
    model = genai.GenerativeModel(GEMINI_MODEL_NAME)
    
    response = model.generate_content(
        f"Detect language. Reply with just: English, Hindi, Spanish, French, German, etc. Text: {text[:200]}"
    )
    return response.text.strip()

def embedding_model_run(): 

    # Google Gemini Embedding
    embeddings = GoogleGenerativeAIEmbeddings(
        model=EMBEDDING_MODEL_NAME,
        google_api_key=GOOGLE_API_KEY
    )
    
    embedding_function = embeddings.embed_query
    fields = [
                SimpleField(
                    name="id",
                    type=SearchFieldDataType.String,
                    key=True,
                    filterable=True,
                    retrievable=True
                ),
                SearchableField(
                    name="content",
                    type=SearchFieldDataType.String,
                    searchable=True,
                    retrievable=True
                ),
                SearchField(
                    name="content_vector",
                    type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                    searchable=True,
                    vector_search_dimensions=len(embedding_function("Text")),
                    vector_search_profile_name="myHnswProfile",
                    retrievable=True
                ),
                SearchableField(
                    name="metadata",
                    type=SearchFieldDataType.String,
                    searchable=True,
                    retrievable=True
                ),
                # Additional field for filtering on document source
                SimpleField(
                    name="source",
                    type=SearchFieldDataType.String,
                    filterable=True,
                    retrievable=True
                ),
                # NEW: Add userid field for user-specific filtering
                SimpleField(
                    name="user_id",
                    type=SearchFieldDataType.String,
                    filterable=True,
                    retrievable=True
                ),
            ]

    index_client = SearchIndexClient(endpoint=AZURE_COGNITIVE_SEARCH_ENDPOINT, credential=AzureKeyCredential(AZURE_COGNITIVE_SEARCH_API_KEY))

    def check_index_exists(index_name=AZURE_COGNITIVE_SEARCH_INDEX_NAME, index_client=index_client):
        try:
            # Attempt to get the index; if it exists, this will succeed
            index = index_client.get_index(index_name)
            return True
        except Exception as e:
            return False

    index_exists = check_index_exists(AZURE_COGNITIVE_SEARCH_INDEX_NAME)
    
    if index_exists:
        print('________________________athena-index-3 Collection / Data already present____________________________ :- ')
        vectordb = AzureSearch(azure_search_endpoint=AZURE_COGNITIVE_SEARCH_ENDPOINT,
                                azure_search_key=AZURE_COGNITIVE_SEARCH_API_KEY,
                                index_name=AZURE_COGNITIVE_SEARCH_INDEX_NAME,
                                embedding_function=embeddings,
                                fields=fields)
        return vectordb
                    
    else:
        print('________________________athena-index-3 Collection is not present, please upload data ________________________')
        return None
    

def translate_if_needed(text, to_language="English"):
    """Simple translation helper using Gemini"""
    model = genai.GenerativeModel(GEMINI_MODEL_NAME)
    
    if to_language == "English":
        system_msg = """Translate the following text to English. If already in English, return as-is. 

                        IMPORTANT RULES:
                        1. Preserve ALL technical terms, system names, commands, and code-like words (like 'zholidaysched') exactly as they are
                        2. Only translate natural language words, not technical/system terms
                        3. Preserve ticket numbers (INC) and any alphanumeric identifiers
                        4. Maintain the exact meaning - don't paraphrase or interpret
                        5. Only return the translated text, no explanations
                        """
    else:
        system_msg = f"""Translate the following text to {to_language}. 

                        IMPORTANT RULES:
                        1. Preserve ALL technical terms, system names, commands, and code-like words exactly as they are
                        2. Only translate natural language words, not technical/system terms  
                        3. Preserve ticket numbers (INC), URLs, and any alphanumeric identifiers
                        4. Maintain the exact meaning - don't paraphrase or interpret
                        5. Only return the translated text, no explanations"""
    
    response = model.generate_content(f"{system_msg}\n\nText to translate: {text}")
    return response.text.strip()

async def query_function(vectordb, query, userid=None):
    qa_template = """Answer in Details the user's question using the provided context and history. Use simple, clear language without technical jargon.

        After your answer, list only the ticket numbers (start with INC + 7 numbers) that have similar descriptions to the user's question (separate each ticket with '\n').

        Important guidelines:
        - Check complete question to identfy language.
        - Maintain structure of output as would be in english.
        - For simple questions or greetings without context, provide appropriate responses without showing ticket numbers.
        - Do not create or hallucinate ticket numbers. provide if only present in context with heading '#### Historical ticket numbers' - (separate each ticket with '\n') else ignore.
        - only provide ticket number from contex if not present then dont provide it.

        Context: {context}
        Question: {question}

        """

    prompt = PromptTemplate(template=qa_template,
                                input_variables=['context', 'question'])

    memory = ConversationBufferMemory(
        memory_key="history",
        input_key="question"
    )

    # Use Google Gemini LLM
    llm = ChatGoogleGenerativeAI(
        model=GEMINI_MODEL_NAME,
        google_api_key=GOOGLE_API_KEY,
        temperature=0.0
    )

    # NEW: Configure retriever with userid filtering if provided
    if userid:
        search_kwargs = {
            "score_threshold": .80,
            "filters": f"user_id eq '{userid}'"  # Filter by userid
        }
    else:
        search_kwargs = {"score_threshold": .80}
    
    vectordb = vectordb.as_retriever(
        search_type="similarity_score_threshold", 
        search_kwargs=search_kwargs
    )

    llm = RetrievalQA.from_chain_type(llm=llm,
                                    chain_type='stuff',
                                    retriever=vectordb,
                                    return_source_documents = True,
                                    chain_type_kwargs={
                                    "prompt": prompt,
                                    "memory": memory 
                                    })

    # Function to get responses from Gemini model
    def get_gemini_response(query):
        # Detect original language
        if multilingual:
            original_lang = detect_language(query)
        else :
            original_lang = "English"

        if original_lang.lower() != 'english':
            # For the fallback case, also handle translation
            english_query = translate_if_needed(query, "English")
            english_response = llm({'query': english_query})
            final_response = translate_if_needed(english_response['result'], original_lang)
            response = {'result': final_response, 'source_documents': english_response.get('source_documents', '')}
        else:
            response = llm({'query': query})

        return response

    def extract_source_document(response):
        # Extract source names from the documents
        source_names = []
        for document in response['source_documents']:
            # Extract the source name using regular expression
            try:
                source_names.append(document.metadata['source'])
            except: pass
        return '\n\n'.join(list(set(source_names)))

    final_response =  get_gemini_response(query)
    sources = extract_source_document(final_response)

    return final_response["result"], sources