from fastapi import APIRouter, HTTPException, Request
import jwt
from config import API_KEYS
from schemas.api_schema import RequestBody
from services.query_service import (
    embedding_model_run,
    query_function
)

router = APIRouter()

@router.post("/main-query-function")
async def main_query_function(
    request: Request,
    userRequest: RequestBody
):
    """
    Query documents in the Azure Cognitive Search index (athena-index-4)
    
    - **query**: Question/query to ask about the documents
    - **userid**: Optional user identifier to filter results to user's documents only
    """
    
    try:
        # Extract user_id from JWT token if available, otherwise use default
        auth_header = request.headers.get("authorization")
        if auth_header:
            token = auth_header.partition(" ")[2]
            try:
                user_id = jwt.decode(
                    token,
                    options={"verify_signature": False},  # Skip signature verification
                    algorithms=["HS256", "RS256", "ES256"]  # Allow common algorithms
                ).get("email")
            except Exception as e:
                print(f"JWT decode error: {e}")
                user_id = "<EMAIL>"  # Default user for testing
        else:
            user_id = "<EMAIL>"  # Default user for testing
        # Get vector database
        vectordb = embedding_model_run()
        
        if vectordb is None:
            raise HTTPException(
                status_code=404, 
                detail=f"athena-index-4 not found. Please upload documents first."
            )
        
        # NEW: Pass userid to query_function for filtering
        response, sources = await query_function(
            vectordb, 
            userRequest.query, 
            userid=user_id  # Pass userid for filtering (can be None)
        )

        result = {
            'result': response, 
            'source_documents': sources,
        }

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")