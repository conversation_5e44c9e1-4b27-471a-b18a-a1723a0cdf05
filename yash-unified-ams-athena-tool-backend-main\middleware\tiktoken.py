# middleware/tiktoken.py

import os
import uuid
import jwt
import logging
import json
from datetime import datetime, timezone
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import StreamingResponse

import tiktoken
from utils.kafka import kafka_logger

logger = logging.getLogger(__name__)

# --- NEW: Define the custom separator as a constant for clarity and easy maintenance ---
CUSTOM_TOKEN_SEPARATOR = "$YashUnified2025$"

class TokenCounterMiddleware(BaseHTTPMiddleware):
    """
    Middleware to count tokens for JSON and multipart/form-data requests,
    using tiktoken's efficient token counting methods,
    handling streaming and normal responses with minimal overhead.
    """
    def __init__(self, app):
        super().__init__(app)
        config_name = os.getenv("TIKTOKEN_DEFAULT_MODEL", "cl100k_base")
        try:
            self.encoding = tiktoken.encoding_for_model(config_name)
        except KeyError:
            try:
                self.encoding = tiktoken.get_encoding(config_name)
            except KeyError:
                logger.warning(
                    f"Model or encoding '{config_name}' not found. "
                    f"Falling back to 'cl100k_base'."
                )
                self.encoding = tiktoken.get_encoding("cl100k_base")
        
        # --- NEW: Load the server name from environment variables ---
        self.server_name = os.getenv("SERVER_NAME", "Unknown Agent")

    def _extract_text_from_request(self, data: dict) -> str:
        """Extract prompt text preserving chat structure efficiently."""
        if not isinstance(data, dict):
            return ""
        if "messages" in data and isinstance(data["messages"], list):
            return "\n".join(msg.get("content", "") for msg in data["messages"] if isinstance(msg, dict))
        for key in ["query", "prompt", "input", "message"]:
            if isinstance(data.get(key), str):
                return data[key]
        return ""

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        print("\n" + "="*50)
        print("--- [FASTAPI TOKEN COUNTER] Middleware Activated ---")
        print(f"--- [FASTAPI TOKEN COUNTER] Request Path: {request.url.path}")

        body_bytes = await request.body()
        async def receive():
            return {"type": "http.request", "body": body_bytes}
        request_for_endpoint = Request(request.scope, receive)

        prompt_text, model_name, session_id, user_email = "", "N/A", "N/A", "N/A"
        
        # --- NEW: Initialize variables for your custom data ---
        encrypted_payload = "N/A"
        server_origin = request.headers.get("host", "N/A") # Get server origin from Host header

        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            full_token_string = auth_header.split(" ", 1)[1] # Split only once

            # --- MODIFIED: Logic to handle the custom token format ---
            jwt_part = full_token_string
            if CUSTOM_TOKEN_SEPARATOR in full_token_string:
                parts = full_token_string.split(CUSTOM_TOKEN_SEPARATOR, 1)
                jwt_part = parts[0]
                encrypted_payload = parts[1] # This is the encrypted string after the separator
            
            try:
                # Decode only the JWT part of the token
                decoded_token = jwt.decode(jwt_part, options={"verify_signature": False})
                user_email = decoded_token.get("email") or decoded_token.get("sub") or "N/A"
            except jwt.PyJWTError as e:
                logger.warning(f"Could not decode JWT token: {e}")
                pass # Continue even if JWT is invalid, as we might have other info

        try:
            request_data = {}
            content_type = request.headers.get("content-type", "")
            if body_bytes:
                parser_request = Request(request.scope, receive)
                if "application/json" in content_type:
                    request_data = await parser_request.json()
                elif "multipart/form-data" in content_type:
                    form_data = await parser_request.form()
                    request_data = dict(form_data.items())

            if request_data:
                prompt_text = self._extract_text_from_request(request_data)
                model_name = request_data.get("model", "N/A")
                session_id = request_data.get("session_id", "N/A")

        except Exception as e:
            logger.error(f"Failed to parse request body for token counting: {e}", exc_info=True)

        prompt_tokens = len(self.encoding.encode_ordinary(prompt_text)) if prompt_text else 0

        print(f"--- [FASTAPI TOKEN COUNTER] User Email: {user_email}")
        print(f"--- [FASTAPI TOKEN COUNTER] Session ID: {session_id}")
        # --- NEW: Print the newly extracted data for debugging ---
        print(f"--- [FASTAPI TOKEN COUNTER] Encrypted Payload Found: {'Yes' if encrypted_payload != 'N/A' else 'No'}")
        print(f"--- [FASTAPI TOKEN COUNTER] Server Origin: {server_origin}")
        print(f"--- [FASTAPI TOKEN COUNTER] Server Name: {self.server_name}")
        print(f"--- [FASTAPI TOKEN COUNTER] Prompt Text: '{prompt_text[:100]}...'")
        print(f"--- [FASTAPI TOKEN COUNTER] Prompt Tokens: {prompt_tokens}")
        print("--- [FASTAPI TOKEN COUNTER] Executing endpoint...")

        response = await call_next(request_for_endpoint)
        
        print("--- [FASTAPI TOKEN COUNTER] Endpoint finished. Processing response...")

        # --- MODIFIED: Add your new attributes to the base log entry ---
        base_log_entry = {
            "log_id": str(uuid.uuid4()),
            "user_email": user_email,
            "session_id": session_id,
            "model_name": model_name,
            "request_path": request.url.path,
            "status_code": response.status_code,
            "prompt_tokens": prompt_tokens,
            "encoding_model": self.encoding.name,
            "timestamp_utc": datetime.now(timezone.utc).isoformat(),
            "encrypted_payload": encrypted_payload, 
            "server_origin": server_origin,        
            "server_name": self.server_name        
        }

        if isinstance(response, StreamingResponse):
            async def logging_iterator():
                completion_chunks = []
                async for chunk in response.body_iterator:
                    yield chunk
                    completion_chunks.append(chunk)

                full_completion_text = b"".join(completion_chunks).decode("utf-8", errors="ignore")
                completion_tokens = len(self.encoding.encode_ordinary(full_completion_text))
                log_entry = {**base_log_entry, "completion_tokens": completion_tokens, "total_tokens": prompt_tokens + completion_tokens}
                
                print(f"--- [FASTAPI TOKEN COUNTER] Completion Text (Streaming): '{full_completion_text[:100]}...'")
                print(f"--- [FASTAPI TOKEN COUNTER] Completion Tokens (Streaming): {completion_tokens}")
                print("--- [FASTAPI TOKEN COUNTER] Assembled Log Entry (Streaming):")
                print(json.dumps(log_entry, indent=2))
                
                kafka_logger.log(log_entry)
                
                print("--- [FASTAPI TOKEN COUNTER] Middleware Finished ---")
                print("="*50 + "\n")

            return StreamingResponse(logging_iterator(), status_code=response.status_code, headers=dict(response.headers), media_type=response.media_type)
        else:
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk

            completion_text = response_body.decode("utf-8", errors="ignore")
            completion_tokens = len(self.encoding.encode_ordinary(completion_text))
            log_entry = {**base_log_entry, "completion_tokens": completion_tokens, "total_tokens": prompt_tokens + completion_tokens}
            
            print(f"--- [FASTAPI TOKEN COUNTER] Completion Text: '{completion_text[:100]}...'")
            print(f"--- [FASTAPI TOKEN COUNTER] Completion Tokens: {completion_tokens}")
            print("--- [FASTAPI TOKEN COUNTER] Assembled Log Entry:")
            print(json.dumps(log_entry, indent=2))
            
            kafka_logger.log(log_entry)
            
            print("--- [FASTAPI TOKEN COUNTER] Middleware Finished ---")
            print("="*50 + "\n")

            return Response(content=response_body, status_code=response.status_code, headers=dict(response.headers), media_type=response.media_type)