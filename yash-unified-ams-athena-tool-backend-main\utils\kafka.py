# utils/kafka.py

import os
import json
import logging
import atexit

try:
    from kafka import KafkaProducer
    from kafka.errors import NoBrokersAvailable
    KAFKA_INSTALLED = True
except ImportError:
    KAFKA_INSTALLED = False
    class NoBrokersAvailable(Exception): pass

logger = logging.getLogger(__name__)

class KafkaLogger:
    """A production-grade logger that sends dictionary payloads to a Kafka topic."""
    def __init__(self):
        self.producer = None
        if not KAFKA_INSTALLED:
            logger.critical("Dependency 'kafka-python' is not installed. Kafka logging is disabled.")
            return

        bootstrap_servers = os.getenv("KAFKA_BOOTSTRAP_SERVERS")
        self.topic = os.getenv("KAFKA_TOPIC_NAME")

        if not bootstrap_servers or not self.topic:
            logger.critical("KAFKA_BOOTSTRAP_SERVERS or KAFKA_TOPIC_NAME is not set. Kafka logging disabled.")
            return

        try:
            logger.info(f"Connecting KafkaProducer to bootstrap servers: {bootstrap_servers}")
            self.producer = KafkaProducer(
                bootstrap_servers=bootstrap_servers.split(','),
                value_serializer=lambda v: json.dumps(v, default=str).encode('utf-8'),
                key_serializer=lambda k: k.encode('utf-8') if k else None,
                retries=5,
                request_timeout_ms=30000,
                acks='all'
            )
            logger.info(f"KafkaProducer connected. Logging to topic '{self.topic}'.")
        except NoBrokersAvailable:
            logger.critical(f"FATAL: Could not connect to any Kafka brokers at '{bootstrap_servers}'.")
        except Exception as e:
            logger.critical(f"FATAL: Error initializing KafkaProducer: {e}", exc_info=True)

    def log(self, data: dict):
        """Sends a dictionary log entry to the configured Kafka topic."""
        # =========================================================================
        # --- START: DEBUG LOGGING ---
        print(f"--- [KAFKA LOGGER] Sending message to topic '{self.topic}'...")
        # --- END: DEBUG LOGGING ---
        # =========================================================================
        if not self.producer:
            print("--- [KAFKA LOGGER] Producer not available. Message not sent.")
            return
        try:
            self.producer.send(self.topic, key=data.get("session_id"), value=data)
        except Exception as e:
            logger.error(f"Error sending message to Kafka: {e}", exc_info=True)

    def close(self):
        """Flushes buffered messages and safely closes the producer connection."""
        if self.producer:
            logger.info("Flushing remaining messages and closing Kafka producer.")
            self.producer.flush(timeout=10)
            self.producer.close()

kafka_logger = KafkaLogger()
atexit.register(kafka_logger.close)