import urllib.request
import json
import sys

def test_api():
    print("🧪 Testing API with Google API Key...")
    print("=" * 60)
    
    try:
        # Test 1: Root endpoint
        print("Test 1: Root Endpoint")
        response = urllib.request.urlopen('http://localhost:8000/')
        print(f"✅ Status: {response.getcode()}")
        data = response.read().decode()
        print(f"Response: {data[:200]}...")
        print()
        
        # Test 2: Query endpoint (tests Google Gemini integration)
        print("Test 2: Query Endpoint (Google Gemini)")
        query_data = json.dumps({"query": "Hello, this is a test query"}).encode()
        req = urllib.request.Request(
            'http://localhost:8000/main-query-function', 
            data=query_data,
            headers={'Content-Type': 'application/json'}
        )
        response = urllib.request.urlopen(req)
        print(f"✅ Status: {response.getcode()}")
        data = response.read().decode()
        print(f"Response: {data[:300]}...")
        print()
        
        # Test 3: Recommendations endpoint
        print("Test 3: Recommendations Endpoint")
        rec_data = json.dumps({
            "query": '[{"question": "What is AI?", "answer": "AI is artificial intelligence"}]'
        }).encode()
        req = urllib.request.Request(
            'http://localhost:8000/next-question-recommendation', 
            data=rec_data,
            headers={'Content-Type': 'application/json'}
        )
        response = urllib.request.urlopen(req)
        print(f"✅ Status: {response.getcode()}")
        data = response.read().decode()
        print(f"Response: {data[:300]}...")
        print()
        
        print("🎉 All tests completed successfully!")
        print("Your Google API key is working correctly!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_api()
    sys.exit(0 if success else 1)
